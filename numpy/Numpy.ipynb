{"cells": [{"cell_type": "markdown", "id": "954559f1-9217-44f7-ae04-74121b5696af", "metadata": {}, "source": ["EXERCISE 1"]}, {"cell_type": "code", "execution_count": 1, "id": "6c828333-3f79-412b-985a-7fbd9f692593", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "e13e6c15-e8c6-42af-b0bf-89b007955c05", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'int'>\n", "<class 'float'>\n", "<class 'str'>\n", "<class 'dict'>\n", "<class 'list'>\n", "<class 'tuple'>\n", "<class 'set'>\n", "<class 'bool'>\n"]}], "source": ["np_array= np.array([\n", "    10,\n", "    10.5,\n", "    \"What's new\",\n", "    {\"a\": 7, \"b\": 11},\n", "    [1, 2, 3],\n", "    (4, 5),\n", "    {7, 8, 9},\n", "    True\n", "], dtype=object)\n", "\n", "for i in  np_array:\n", "    print(type(i))"]}, {"cell_type": "raw", "id": "db3e8b0e-66d0-4013-94e7-542a9c8a41ab", "metadata": {}, "source": ["EXERCISE 2"]}, {"cell_type": "code", "execution_count": 3, "id": "0c9c03fb-febf-4b56-8551-8f010a2789e9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original Array\n", "[0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", " 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", " 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", " 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", " 0. 0. 0. 0.]\n", "Reshaped Array\n", "[[0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]]\n"]}], "source": ["zeros_array = np.zeros(100)\n", "reshaped_array = zeros_array.reshape(10, 10)\n", "\n", "print(\"Original Array\")\n", "print(zeros_array)\n", "\n", "print(\"Reshaped Array\")\n", "print(reshaped_array)"]}, {"cell_type": "raw", "id": "c963c769-25fa-4362-b913-46171d990a5e", "metadata": {}, "source": ["EXERCISE 3"]}, {"cell_type": "code", "execution_count": 8, "id": "a7aac72b-9835-4d4f-8937-68fd057212e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5x5 array with range from 10-51\n", "[[12 25 26 35 37]\n", " [47 17 37 10 42]\n", " [50 10 35 20 23]\n", " [10 46 39 37 10]\n", " [23 26 47 34 48]]\n"]}], "source": ["random_array = np.random.randint(10,51, size=(5,5))\n", "print(\"5x5 array with range from 10-51\")\n", "print(random_array)"]}, {"cell_type": "raw", "id": "6d25902a-9f2c-4c98-ba86-db9e468cc87b", "metadata": {}, "source": ["EXERCISE 4: Identity Matrix"]}, {"cell_type": "code", "execution_count": 11, "id": "0021cc99-a83c-448f-b49c-01e2d89c3ed3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4x4 identity matrix:\n", "[[1. 0. 0. 0.]\n", " [0. 1. 0. 0.]\n", " [0. 0. 1. 0.]\n", " [0. 0. 0. 1.]]\n"]}], "source": ["identity_matrix = np.eye(4)\n", "print(\"4x4 identity matrix:\")\n", "print(identity_matrix)"]}, {"cell_type": "raw", "id": "ecfe48f6-cc75-4e7f-aab6-5edba95d15f2", "metadata": {}, "source": ["EXERCISE 5: Array Slicing"]}, {"cell_type": "code", "execution_count": 14, "id": "60dd27a5-0492-4942-80bd-d9011da0ccb3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[  1   2   3   4   5   6   7   8   9  10  11  12  13  14  15  16  17  18\n", "  19  20  21  22  23  24  25  26  27  28  29  30  31  32  33  34  35  36\n", "  37  38  39  40  41  42  43  44  45  46  47  48  49  50  51  52  53  54\n", "  55  56  57  58  59  60  61  62  63  64  65  66  67  68  69  70  71  72\n", "  73  74  75  76  77  78  79  80  81  82  83  84  85  86  87  88  89  90\n", "  91  92  93  94  95  96  97  98  99 100]\n"]}], "source": ["arr = np.arange(1, 101)\n", "print(arr)"]}, {"cell_type": "code", "execution_count": 15, "id": "cd3c4c71-2f06-457b-92e7-d8f52cb9f4d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 1  3  5  7  9 11 13 15 17 19 21 23 25 27 29 31 33 35 37 39 41 43 45 47\n", " 49 51 53 55 57 59 61 63 65 67 69 71 73 75 77 79 81 83 85 87 89 91 93 95\n", " 97 99]\n"]}], "source": ["odd_num_array = arr[::2]\n", "print(odd_num_array)"]}, {"cell_type": "code", "execution_count": 17, "id": "8a42ec72-340c-4b33-a632-02690d359b8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[100  98  96  94  92  90  88  86  84  82  80  78  76  74  72  70  68  66\n", "  64  62  60  58  56  54  52  50  48  46  44  42  40  38  36  34  32  30\n", "  28  26  24  22  20  18  16  14  12  10   8   6   4   2]\n"]}], "source": ["even_num_reversed_arr = arr[1::2][::-1]\n", "print(even_num_reversed_arr)"]}, {"cell_type": "code", "execution_count": 18, "id": "0aa4bdbc-ebfb-4620-b178-2a0ac2b36531", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[  1   0   3   4   0   6   7   0   9  10   0  12  13   0  15  16   0  18\n", "  19   0  21  22   0  24  25   0  27  28   0  30  31   0  33  34   0  36\n", "  37   0  39  40   0  42  43   0  45  46   0  48  49   0  51  52   0  54\n", "  55   0  57  58   0  60  61   0  63  64   0  66  67   0  69  70   0  72\n", "  73   0  75  76   0  78  79   0  81  82   0  84  85   0  87  88   0  90\n", "  91   0  93  94   0  96  97   0  99 100]\n"]}], "source": ["arr_mod = arr.copy()\n", "arr_mod[1::3] = 0\n", "print(arr_mod)"]}, {"cell_type": "raw", "id": "ae43e8fe-244d-4909-a3b8-162ae843dbb0", "metadata": {}, "source": ["EXERCISE 6: Random"]}, {"cell_type": "code", "execution_count": 22, "id": "ff09dc41-d30c-4d2b-99ad-18a035f9d6cd", "metadata": {}, "outputs": [], "source": ["np.random.seed(888)"]}, {"cell_type": "code", "execution_count": 23, "id": "a94f1786-f540-4db5-bcd7-ec399633ee34", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-1.76200874e-01  1.88876361e-01  8.26747180e-01 -3.24473146e-02\n", " -6.52499418e-01 -1.05339382e-01  2.17776116e-01  5.87281504e-01\n", "  1.00237892e-01 -1.09994668e+00 -2.55305389e-01  4.05304381e-01\n", "  1.62663953e-01  1.04163462e+00  2.24324184e-01  6.99304449e-01\n", " -8.65543512e-01 -1.39346831e+00 -2.36687913e-01 -7.57041911e-01\n", "  1.31429195e+00 -9.77385899e-01 -3.59032576e-01  1.16141585e-01\n", " -3.51081138e-01 -4.39838344e-01 -4.90995473e-02 -4.31596770e-01\n", " -1.08875099e+00 -2.31088106e+00 -1.86236420e+00  2.34708674e-01\n", " -8.95585921e-01 -5.40834525e-01 -4.02399061e-01  2.31458561e-02\n", "  1.77688506e-01 -5.73309272e-01 -3.38392138e-01 -2.64422114e-03\n", "  1.44930339e+00  1.36605641e+00  2.69187461e+00  6.76065035e-01\n", " -1.06098418e+00  1.31845313e-01  1.71934297e-01  8.88062794e-02\n", "  1.03963016e+00 -1.37846942e-01 -3.69232844e-01  3.19587912e+00\n", "  1.11457451e+00 -2.59516296e-01  1.48370897e+00 -1.57049065e+00\n", "  9.78899330e-01  1.76070681e+00  3.53121953e-01 -3.08510762e-01\n", " -1.40481067e+00 -6.54547811e-01 -1.37791290e+00 -2.42605914e-01\n", " -1.17542916e+00  6.96400848e-01  2.84105779e-01 -1.01948195e+00\n", "  1.09675079e+00  2.41646711e-01 -3.67039678e-01 -2.19894776e+00\n", "  1.41872937e+00  2.20794136e-01  7.92524915e-01 -2.23775806e-01\n", " -6.96694068e-01 -9.17717925e-01 -1.47199487e+00  1.87770559e+00\n", "  1.51033108e+00  1.18732817e-01  6.50728248e-01 -7.07655562e-01\n", "  1.69862924e+00  1.34663117e+00  5.26657670e-01 -6.13561456e-01\n", " -1.79179012e-01 -1.28634991e+00  1.41675325e+00 -1.81351151e-01\n", " -1.02037575e-01 -6.03250569e-01  1.20489600e+00  9.62265746e-01\n", "  8.61170433e-01  1.96244237e+00 -1.15548773e+00  3.45259423e-01]\n"]}], "source": ["normal_array = np.random.randn(100)\n", "print(normal_array)"]}, {"cell_type": "code", "execution_count": 29, "id": "8957ed4e-9b2f-4288-a992-b81cdf37e8ce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 9  7  5  2  5 10  2  5]\n", " [ 2  3  8  6  4  7  1  3]\n", " [ 6  3  6  2  8  4  1  2]\n", " [10  1  1  1 10  8  9  1]\n", " [ 6  5  9  3  4  4  8  2]\n", " [ 4  2  4  3  6  2  7  9]\n", " [ 3  5  9  4 10  7  8  1]\n", " [ 7  8  4  6  5 10  4  7]]\n"]}], "source": ["uniform2d_array = np.random.randint(1,11, (8, 8))\n", "print(uniform2d_array)"]}, {"cell_type": "code", "execution_count": 31, "id": "c8f913ec-c1d6-419e-a218-43131045a906", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[15 13 14 15 14]\n", "  [ 2 15 16 16 12]]\n", "\n", " [[ 2 13  8  9  5]\n", "  [17 17  9  1 16]]\n", "\n", " [[ 3  1 14 17  2]\n", "  [ 1  4  4 17  1]]\n", "\n", " [[ 2  4  9 10 15]\n", "  [ 3 16 17  2 10]]]\n"]}], "source": ["uniform3D_array = np.random.randint(1, 18, (4,2,5))\n", "print(uniform3D_array)"]}, {"cell_type": "raw", "id": "ef81c5e6-2569-4fda-a256-a225ad358550", "metadata": {}, "source": ["Exercise 5: Split, concatenate, reshape arrays"]}, {"cell_type": "code", "execution_count": 44, "id": "e0ad9b06-44f3-4950-b1d8-5b1e8eb80d20", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 1  2  3  4  5  6  7  8  9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24\n", " 25 26 27 28 29 30 31 32 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48\n", " 49 50]\n", "[ 51  52  53  54  55  56  57  58  59  60  61  62  63  64  65  66  67  68\n", "  69  70  71  72  73  74  75  76  77  78  79  80  81  82  83  84  85  86\n", "  87  88  89  90  91  92  93  94  95  96  97  98  99 100]\n", "Concatenate array1 and array2\n", "[  1   2   3   4   5   6   7   8   9  10  11  12  13  14  15  16  17  18\n", "  19  20  21  22  23  24  25  26  27  28  29  30  31  32  33  34  35  36\n", "  37  38  39  40  41  42  43  44  45  46  47  48  49  50  51  52  53  54\n", "  55  56  57  58  59  60  61  62  63  64  65  66  67  68  69  70  71  72\n", "  73  74  75  76  77  78  79  80  81  82  83  84  85  86  87  88  89  90\n", "  91  92  93  94  95  96  97  98  99 100]\n", "Reshaped to a 10x10\n", "[[  1   2   3   4   5   6   7   8   9  10]\n", " [ 11  12  13  14  15  16  17  18  19  20]\n", " [ 21  22  23  24  25  26  27  28  29  30]\n", " [ 31  32  33  34  35  36  37  38  39  40]\n", " [ 41  42  43  44  45  46  47  48  49  50]\n", " [ 51  52  53  54  55  56  57  58  59  60]\n", " [ 61  62  63  64  65  66  67  68  69  70]\n", " [ 71  72  73  74  75  76  77  78  79  80]\n", " [ 81  82  83  84  85  86  87  88  89  90]\n", " [ 91  92  93  94  95  96  97  98  99 100]]\n"]}], "source": ["arr1 = np.arange(1, 51)\n", "arr2 = np.arange(51, 101)\n", "print(arr1)\n", "print(arr2)\n", "arr1_arr2 = np.concatenate((arr1, arr2))\n", "print(\"Concatenate array1 and array2\")\n", "print(arr1_arr2)\n", "\n", "arr_reshaped = arr1_arr2.reshape(10,10)\n", "print(\"Reshaped to a 10x10\")\n", "print(arr_reshaped)"]}, {"cell_type": "raw", "id": "79422dbe-db4d-475d-8d94-6b76c382fe51", "metadata": {}, "source": ["Exercise 6: Broadcasting and Slicing"]}, {"cell_type": "code", "execution_count": 46, "id": "b886ddac-d11a-4287-a53b-62e0db05c782", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1 1 1 1 1 1 1 1 1]\n", " [1 1 1 1 1 1 1 1 1]\n", " [1 1 1 1 1 1 1 1 1]\n", " [1 1 1 1 1 1 1 1 1]\n", " [1 1 1 1 1 1 1 1 1]\n", " [1 1 1 1 1 1 1 1 1]\n", " [1 1 1 1 1 1 1 1 1]\n", " [1 1 1 1 1 1 1 1 1]\n", " [1 1 1 1 1 1 1 1 1]]\n"]}], "source": ["arr = np.ones((9, 9), dtype=np.int8)\n", "print(arr)"]}, {"cell_type": "code", "execution_count": 47, "id": "b65bdaa2-e107-48b7-aa1e-92f45bff5f13", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1 1 1 1 1 1 1 1 1]\n", " [1 0 0 0 0 0 0 0 1]\n", " [1 0 1 1 1 1 1 0 1]\n", " [1 0 1 0 0 0 1 0 1]\n", " [1 0 1 0 1 0 1 0 1]\n", " [1 0 1 0 0 0 1 0 1]\n", " [1 0 1 1 1 1 1 0 1]\n", " [1 0 0 0 0 0 0 0 1]\n", " [1 1 1 1 1 1 1 1 1]]\n"]}], "source": ["arr[1:-1, 1:-1] = 0\n", "arr[2:-2, 2:-2] = 1\n", "arr[3:-3, 3:-3] = 0\n", "arr[4, 4] = 1\n", "print(arr)"]}, {"cell_type": "code", "execution_count": 51, "id": "47d3961c-23a2-4e99-bbf5-811c7beb356d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 1  2  3]\n", " [ 2  4  6]\n", " [ 3  6  9]\n", " [ 4  8 12]\n", " [ 5 10 15]]\n"]}], "source": ["array_1 = np.array([1,2,3,4,5], dtype=np.int8)\n", "array_2 = np.array([1,2,3], dtype=np.int8)\n", "output = array_1[:, np.newaxis] * array_2\n", "print(output)"]}, {"cell_type": "code", "execution_count": 52, "id": "8ec7b2d6-a232-4041-b287-51bd572ce110", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 7.  1.]\n", " [nan  2.]\n", " [nan  8.]\n", " [ 9.  3.]\n", " [ 8.  9.]\n", " [nan  2.]\n", " [ 8.  2.]\n", " [nan  6.]\n", " [ 9.  2.]\n", " [ 8.  5.]]\n"]}], "source": ["generator = np.random.default_rng(123)\n", "grades = np.round(generator.uniform(low = 0.0, high = 10.0, size = (10, 2)))\n", "grades[[1,2,5,7], [0,0,0,0]] = np.nan\n", "print(grades)"]}, {"cell_type": "code", "execution_count": null, "id": "48560d5c-0edb-48ca-8714-685ffc203ef3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.9"}}, "nbformat": 4, "nbformat_minor": 5}