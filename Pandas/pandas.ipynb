{"cells": [{"cell_type": "raw", "id": "fec53ea7-6489-41b5-9c6b-aa1a34f4565c", "metadata": {}, "source": ["Exercise 1: Your first DataFrame"]}, {"cell_type": "code", "execution_count": 23, "id": "40e1ab20-0157-4e25-be37-25383c1e264c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 24, "id": "e7d4b584-4971-418a-a62a-9c924546e4d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   color     list number\n", "1   Blue   [1, 2]    1.1\n", "3    Red   [3, 4]    2.2\n", "5   Pink   [5, 6]    3.3\n", "7   <PERSON>   [7, 8]    4.4\n", "9  Black  [9, 10]    5.5\n"]}], "source": ["#Numpy array\n", "data = np.array([\n", "    ['Blue', [1, 2], 1.1],\n", "    ['<PERSON>', [3, 4], 2.2],\n", "    ['<PERSON>', [5, 6], 3.3],\n", "    ['<PERSON>', [7, 8], 4.4],\n", "    ['Black', [9, 10], 5.5]\n", "], dtype=object)\n", "\n", "df_numpy = pd.DataFrame(data, columns=['color', 'list', 'number'], index=[1, 3, 5, 7, 9])\n", "print(df_numpy)"]}, {"cell_type": "code", "execution_count": 25, "id": "b48fceb1-9e45-4a20-8c1e-a503ea0259b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   color     list  number\n", "1   Blue   [1, 2]     1.1\n", "3    Red   [3, 4]     2.2\n", "5   Pink   [5, 6]     3.3\n", "7   <PERSON>   [7, 8]     4.4\n", "9  Black  [9, 10]     5.5\n"]}], "source": ["# Pandas series\n", "color = pd.Series(['<PERSON>', '<PERSON>', 'Pink', 'Grey', 'Black'], index=[1, 3, 5, 7, 9])\n", "list_ = pd.Series([[1,2], [3, 4], [5,6], [7,8], [9, 10]], index=[1, 3, 5, 7, 9]) \n", "number = pd.Series([1.1, 2.2, 3.3, 4.4, 5.5], index=[1, 3, 5, 7, 9])\n", "\n", "df_Series = pd.DataFrame({\n", "    'color': color,\n", "    'list': list_,\n", "    'number': number\n", "})\n", "\n", "print(df_Series)"]}, {"cell_type": "raw", "id": "e05bb6d3-c58a-4a79-9477-e70de389e259", "metadata": {}, "source": ["Exercise 2: Electric power consumption"]}, {"cell_type": "code", "execution_count": 28, "id": "244d3bba-6cf7-40a1-8b0d-7cd4226e4f46", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Overview:\n", "\n", "       Global_active_power  Global_reactive_power       Voltage  \\\n", "count         2.049280e+06           2.049280e+06  2.049280e+06   \n", "mean          1.091615e+00           1.237145e-01  2.408399e+02   \n", "std           1.057294e+00           1.127220e-01  3.239987e+00   \n", "min           7.600000e-02           0.000000e+00  2.232000e+02   \n", "25%           3.080000e-01           4.800000e-02  2.389900e+02   \n", "50%           6.020000e-01           1.000000e-01  2.410100e+02   \n", "75%           1.528000e+00           1.940000e-01  2.428900e+02   \n", "max           1.112200e+01           1.390000e+00  2.541500e+02   \n", "\n", "       Global_intensity  Sub_metering_1  \n", "count      2.049280e+06    2.049280e+06  \n", "mean       4.627759e+00    1.121923e+00  \n", "std        4.444396e+00    6.153031e+00  \n", "min        2.000000e-01    0.000000e+00  \n", "25%        1.400000e+00    0.000000e+00  \n", "50%        2.600000e+00    0.000000e+00  \n", "75%        6.400000e+00    0.000000e+00  \n", "max        4.840000e+01    8.800000e+01  \n", "\n", "Filtered rows (Date >= 2008-12-27 and Voltage >= 242):\n", "            Global_active_power  Global_reactive_power  Voltage  \\\n", "Date                                                              \n", "2008-12-27                0.996                  0.066   244.81   \n", "2008-12-27                1.076                  0.162   244.78   \n", "2008-12-27                1.064                  0.172   244.74   \n", "2008-12-27                1.070                  0.174   245.28   \n", "2008-12-27                0.804                  0.184   246.30   \n", "\n", "            Global_intensity  Sub_metering_1  \n", "Date                                          \n", "2008-12-27               4.0            0.06  \n", "2008-12-27               4.4            0.06  \n", "2008-12-27               4.4            0.06  \n", "2008-12-27               4.4            0.06  \n", "2008-12-27               3.4            0.06  \n", "\n", "88888th row:\n", "Global_active_power        0.254\n", "Global_reactive_power      0.000\n", "Voltage                  238.100\n", "Global_intensity           1.200\n", "Sub_metering_1             0.060\n", "Name: 2007-02-16 00:00:00, dtype: float64\n", "\n", "Date with maximal Global_active_power: 2009-02-22 (Value: 11.122)\n", "\n", "First few rows after sorting first three columns:\n", "            Global_active_power  Global_reactive_power  Voltage\n", "Date                                                           \n", "2009-02-22               11.122                  0.174   229.78\n", "2007-03-04               10.670                  0.516   230.20\n", "2007-03-04               10.650                  0.500   229.97\n", "2009-02-22               10.536                  0.192   230.24\n", "2008-11-30               10.348                  0.084   231.60\n", "\n", "Daily average of Global_active_power:\n", "Date\n", "2006-12-16    3.053475\n", "2006-12-17    2.354486\n", "2006-12-18    1.530435\n", "2006-12-19    1.157079\n", "2006-12-20    1.545658\n", "Freq: D, Name: Global_active_power, dtype: float64\n"]}], "source": ["# Load dataset\n", "df = pd.read_csv(\n", "    \"household_power_consumption.csv\",\n", "    sep=';',\n", "    low_memory=False,\n", "    na_values='?'\n", ")\n", "\n", "#Delete the columns Time, Sub_metering_2, and Sub_metering_3\n", "df = df.drop(columns=[\"Time\", \"Sub_metering_2\", \"Sub_metering_3\"], errors='ignore')\n", "\n", "#Set Date as index\n", "df[\"Date\"] = pd.to_datetime(df[\"Date\"], dayfirst=True, errors=\"coerce\")\n", "df = df.set_index(\"Date\")\n", "\n", "#Create a function to update types\n", "def update_types(df):\n", "    # Convert all numeric columns to float where possible\n", "    for col in df.columns:\n", "        df[col] = pd.to_numeric(df[col], errors='coerce')\n", "    return df\n", "\n", "df = update_types(df)\n", "\n", "#Overview of the dataset\n", "print(\"Dataset Overview:\\n\")\n", "print(df.describe())\n", "\n", "#Delete rows with missing values\n", "df = df.dropna()\n", "\n", "#Modify Sub_metering_1 by (x + 1) * 0.06\n", "if \"Sub_metering_1\" in df.columns:\n", "    df[\"Sub_metering_1\"] = (df[\"Sub_metering_1\"] + 1) * 0.06\n", "\n", "#Select rows where Date >= 2008-12-27 and Voltage >= 242\n", "filtered = df.loc[(df.index >= \"2008-12-27\") & (df[\"Voltage\"] >= 242)]\n", "\n", "print(\"\\nFiltered rows (Date >= 2008-12-27 and Voltage >= 242):\")\n", "print(filtered.head())\n", "\n", "#Print the 88888th row\n", "try:\n", "    print(\"\\n88888th row:\")\n", "    print(df.iloc[88887])  # Indexing starts at 0\n", "except IndexError:\n", "    print(\"\\nDataset has less than 88888 rows.\")\n", "\n", "#Find the date for which Global_active_power is maximal\n", "max_power_date = df[\"Global_active_power\"].idxmax()\n", "max_power_value = df[\"Global_active_power\"].max()\n", "print(f\"\\nDate with maximal Global_active_power: {max_power_date.date()} (Value: {max_power_value})\")\n", "\n", "#Sort first three columns\n", "sorted_df = df.sort_values(by=[\"Global_active_power\", \"Voltage\"], ascending=[False, True])\n", "sorted_subset = sorted_df.iloc[:, :3]\n", "print(\"\\nFirst few rows after sorting first three columns:\")\n", "print(sorted_subset.head())\n", "\n", "#Compute daily average of Global_active_power\n", "daily_avg = df[\"Global_active_power\"].resample(\"D\").mean()\n", "print(\"\\nDaily average of Global_active_power:\")\n", "print(daily_avg.head())\n", "\n"]}, {"cell_type": "raw", "id": "d14d4a1f-4e0d-484b-98a4-b91fe46f8849", "metadata": {}, "source": ["Exercise 3: E-commerce purchases"]}, {"cell_type": "code", "execution_count": 63, "id": "0ca8cc4c-a5a6-4b28-9b79-8ea03d68d6d7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 10000 rows and 14 columns\n", "Average Purchase Price:$50.35\n", "Highest Purchase Price: $99.99 \n", "Lowest Purchase Price: $0.0\n", "No of English Users: 1098\n", "No of lawyers: 30\n", "\n", "Purchases made during AM or PM\n", "AM or PM\n", "PM    5068\n", "AM    4932\n", "Name: count, dtype: int64\n", "\n", "Top 5 common jobs\n", "Job\n", "Interior and spatial designer        31\n", "Lawyer                               30\n", "Social researcher                    28\n", "Research officer, political party    27\n", "Designer, jewellery                  27\n", "Name: count, dtype: int64\n", "\n", "Purchase price for Lot 90WT: 75.1\n", "\n", "Email for Credit Card Holder of number **************** is: <EMAIL>\n", "\n", "People using American Express and purchase above $95: 39\n", "\n", "People with credit cards that expire in 2025: 0\n", "\n", "Top 5 email providers: Email Provider\n", "hotmail.com     1638\n", "yahoo.com       1616\n", "gmail.com       1605\n", "smith.com         42\n", "williams.com      37\n", "Name: count, dtype: int64\n"]}], "source": ["df = pd.read_csv(\"Ecommerce Purchases.csv\")\n", "df.head()\n", "df.shape\n", "rows, columns = df.shape\n", "print(f\"There are {rows} rows and {columns} columns\")\n", "\n", "##average purchase price\n", "avg_price = df[\"Purchase Price\"].mean()\n", "print(f\"Average Purchase Price:${avg_price:.2f}\")\n", "\n", "##highest and lowest purchase prices\n", "highest_price = df[\"Purchase Price\"].max()\n", "lowest_price = df[\"Purchase Price\"].min()\n", "\n", "print(f\"Highest Purchase Price: ${highest_price} \\nLowest Purchase Price: ${lowest_price}\")\n", "\n", "#people with English as language\n", "en_users = df[df[\"Language\"] == \"en\"].shape[0]\n", "print(f\"No of English Users: {en_users}\")\n", "\n", "#No of layers\n", "lawyers = df[df[\"Job\"] == \"Lawyer\"].shape[0]\n", "print(f\"No of lawyers: {lawyers}\")\n", "\n", "#purchases made during AM and PM\n", "buy_time = df[\"AM or PM\"].value_counts()\n", "print(f\"\\nPurchases made during AM or PM\\n{buy_time}\")\n", "\n", "#5 most common job titles\n", "top_job = df[\"Job\"].value_counts().head(5)\n", "print(f\"\\nTop 5 common jobs\\n{top_job}\")\n", "\n", "#purchase on 90WT\n", "lot_price = df[df[\"Lot\"] == \"90 WT\"][\"Purchase Price\"].values[0]\n", "print(f\"\\nPurchase price for Lot 90WT: {lot_price}\")\n", "\n", "#email for the person with credit card number ****************\n", "mail = df[df[\"Credit Card\"] == ****************][\"Email\"].values[0]\n", "print(f\"\\nEmail for Credit Card Holder of number **************** is: {mail}\")\n", "\n", "#America Express Card and purchase above $95\n", "amex_95 = df[(df[\"CC Provider\"] == \"American Express\") & (df[\"Purchase Price\"] > 95)].shape[0]\n", "print(f\"\\nPeople using American Express and purchase above $95: {amex_95}\")\n", "\n", "#Credit card expires in 2025\n", "bb_2025 = df[df[\"CC Exp Date\"].apply(lambda x: x.split('/')[1]) == \"2025\"].shape[0]\n", "print(f\"\\nPeople with credit cards that expire in 2025: {bb_2025}\")\n", "\n", "#most powerful email providers\n", "df[\"Email Provider\"] = df[\"Email\"].apply(lambda x:x.split('@')[1])\n", "top_emails_providers = df[\"Email Provider\"].value_counts().head(5)\n", "print(f\"\\nTop 5 email providers: {top_emails_providers}\")"]}, {"cell_type": "raw", "id": "23f6e25c-3f3e-4934-94ca-72ff6e59dd7f", "metadata": {}, "source": ["Exercise 4: Handling missing values"]}, {"cell_type": "code", "execution_count": 74, "id": "279d37cf-8a80-406f-ac0b-524f2b2cbbc1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sepal_length    0\n", "sepal_width     0\n", "petal_length    0\n", "petal_width     0\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sepal_length</th>\n", "      <th>sepal_width</th>\n", "      <th>petal_length</th>\n", "      <th>petal_width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5.1</td>\n", "      <td>3.5</td>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.9</td>\n", "      <td>3.0</td>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.7</td>\n", "      <td>3.2</td>\n", "      <td>1.3</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.6</td>\n", "      <td>3.1</td>\n", "      <td>1.5</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.0</td>\n", "      <td>-3.6</td>\n", "      <td>-1.4</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sepal_length  sepal_width  petal_length  petal_width\n", "0           5.1          3.5           1.4          0.2\n", "1           4.9          3.0           1.4          0.2\n", "2           4.7          3.2           1.3          0.2\n", "3           4.6          3.1           1.5          0.2\n", "4           5.0         -3.6          -1.4          0.2"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(\"iris_missing.csv\")\n", "df.head()\n", "\n", "#drop flower column\n", "df =df.drop(\"flower\", axis=1)\n", "\n", "# Convert all numeric columns to numbers && Errors='coerce' will turn non-numeric values (like strings) into NaN\n", "for col in [\"sepal_length\", \"sepal_width\", \"petal_length\", \"petal_width\"]:\n", "    df[col] = pd.to_numeric(df[col], errors='coerce')\n", "\n", "#finding missing values and fill them\n", "df = df.fillna({\n", "    \"sepal_length\": df[\"sepal_length\"].mean(),\n", "    \"sepal_width\": df[\"sepal_width\"].median(),\n", "    \"petal_length\": 0,\n", "    \"petal_width\": 0\n", "})\n", "\n", "#verify missing values are handled\n", "print(df.isna().sum())\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "03e1ea0f-7027-4482-bc9b-07010024ccfc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.9"}}, "nbformat": 4, "nbformat_minor": 5}